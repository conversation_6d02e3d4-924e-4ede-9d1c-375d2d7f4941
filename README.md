# SuperNuggets Master - 智能应用自动化助手

## 📋 项目概述

**项目名称**: SuperNuggets Master - 智能应用自动化助手  
**项目目标**: 开发一款Android自动化应用，能够模拟真实用户操作，支持快手极速版、抖音极速版、阴阳师、支付宝等应用的自动化任务执行  
**核心理念**: 实用性优先、轻量级实现、高兼容性  
**设计风格**: 米家风格青绿色主题，简洁现代的用户界面

## ✨ 核心功能特性

### 🎯 脚本管理系统
- **注解驱动**: 使用@ScriptModel、@ScriptPage、@ScriptIcons等注解声明脚本，代码简洁易读
- **智能引擎**: ScriptEngine自动加载脚本、启动应用、识别页面、执行逻辑
- **可视化选择**: 用户可通过直观的卡片界面勾选需要执行的脚本
- **批量执行**: 支持按顺序执行多个选中的脚本，提高自动化效率
- **实时状态**: 显示每个脚本的执行状态、进度和预估时间
- **页面识别**: 自动识别当前屏幕是脚本定义的哪个页面，执行相应逻辑

### 🎨 米家设计风格
- **青绿色主题**: 采用舒适的青绿色配色方案，符合米家简约美学
- **圆角卡片**: 大量使用圆角卡片设计，界面层次分明
- **动态反馈**: 丰富的动画效果和状态指示，提升用户体验
- **响应式布局**: 适配不同屏幕尺寸和设备类型

### 🔒 权限管理
- **引导式设置**: 友好的权限开启引导界面
- **实时检测**: 自动检测权限状态，确保功能正常运行
- **一键开启**: 简化权限申请流程，降低使用门槛

### 📊 执行监控
- **实时进度**: 显示脚本执行进度和当前状态
- **结果反馈**: 详细的执行结果和错误信息提示
- **操作控制**: 支持暂停、停止、重新开始等操作

## 📱 使用说明

### 🚀 快速开始
1. **安装应用**: 下载并安装SuperNuggets Master应用
2. **开启权限**: 按照引导开启无障碍服务和悬浮窗权限
3. **选择脚本**: 在主界面勾选需要执行的应用脚本
4. **开始执行**: 点击"执行脚本"按钮，系统将按顺序执行选中的脚本
5. **监控进度**: 实时查看执行状态和进度，可随时停止或重新开始

### 🎮 主界面操作
- **脚本选择**: 点击脚本卡片可切换选择状态
- **全选功能**: 使用"全选"按钮快速选择所有脚本
- **执行控制**: 底部操作栏提供执行、停止、重置等功能
- **状态查看**: 顶部状态卡片显示当前执行情况

### 📋 支持的应用脚本
1. **⚡ 快手极速版**: 自动观看视频、获取金币奖励（预计5分钟）
2. **🎵 抖音极速版**: 自动刷视频、签到获取金币（预计4分钟）
3. **💰 支付宝**: 自动收取蚂蚁森林能量（预计2分钟）
4. **👹 阴阳师**: 自动执行日常任务（预计10分钟）
5. **🛒 淘宝**: 自动收取淘宝体验金、养猫咪（预计3分钟）
6. **📦 京东**: 自动签到、摇钱树、种豆得豆（预计3.5分钟）

### 🖼️ 图像识别功能 (新增)
- **智能识别**: 当ID识别失效时，自动启用图像识别作为备用方案
- **分辨率适配**: 自动适配不同设备分辨率，无需手动调整
- **高精度匹配**: 基于OpenCV的模板匹配算法，识别准确率高
- **性能优化**: 灰度图像处理，识别速度快，资源占用低

#### 图像识别工作原理
1. **模板准备**: 使用1080x2340分辨率设备截取的标准模板图片
2. **实时截图**: 获取当前屏幕截图进行分析
3. **分辨率换算**: 根据设备实际分辨率计算缩放比例
4. **动态阈值**: 自动调整识别置信度，适应不同设备特性
5. **结果输出**: 返回匹配结果和置信度分数

### ⚠️ 注意事项
- 请确保手机电量充足，脚本执行可能需要较长时间
- 建议在WiFi环境下使用，避免消耗过多流量
- 首次使用时请逐个测试脚本，确保功能正常
- 请遵守各应用的使用条款，合理使用自动化功能
- 如遇到问题，可使用"重新开始"功能重置状态
- **图像识别需要媒体投影权限**，请在权限设置中允许屏幕录制

## 🏗️ 技术架构方案

### 核心技术栈
- **开发语言**: Kotlin
- **UI框架**: Jetpack Compose
- **架构模式**: MVVM + Repository Pattern + 脚本引擎
- **异步处理**: Kotlin Coroutines + Flow
- **依赖注入**: Hilt
- **本地存储**: Room + DataStore
- **图像处理**: OpenCV Android 4.11.0 (支持模板匹配和分辨率适配)
- **文字识别**: Google ML Kit

### 脚本引擎架构
```
用户界面 (Jetpack Compose)
    ↓
ScriptViewModel (MVVM)
    ↓
ScriptEngine (核心引擎)
    ├── PageRecognizer (页面识别器)
    │   ├── ID识别 (无障碍服务)
    │   └── 图像识别 (OpenCV模板匹配)
    ├── AppLauncher (应用启动器)
    ├── ImageRecognitionHelper (图像识别助手)
    ├── ScreenshotHelper (屏幕截图工具)
    └── ScriptInfo (注解解析)
```

### 图像识别架构 (新增)
```
PageRecognizer
    ↓
ImageRecognitionHelper
    ├── 模板加载 (从assets加载)
    ├── 分辨率适配 (1080x2340基准)
    ├── OpenCV处理 (灰度转换+模板匹配)
    └── 置信度计算 (动态阈值调整)
    ↓
ScreenshotHelper
    ├── 媒体投影截图
    ├── 图像格式转换
    └── 资源管理
```

### 关键组件设计
1. **ScriptEngine** - 脚本执行引擎核心
2. **PageRecognizer** - 页面识别引擎
3. **AppLauncher** - 应用启动管理器
4. **注解系统** - @ScriptModel、@ScriptPage、@ScriptIcons
5. **AutomationAccessibilityService** - 无障碍服务核心
6. **ConfigurationManager** - 配置管理器

### 脚本执行流程
1. **加载脚本**: ScriptEngine扫描并解析注解声明的脚本类
2. **启动应用**: AppLauncher检查并启动目标应用
3. **页面识别**: PageRecognizer识别当前屏幕是哪个页面
4. **执行逻辑**: 调用对应页面的处理方法
5. **循环执行**: 重复步骤3-4，直到脚本逻辑完成

## 🎯 实施阶段规划

### 第一阶段：基础框架搭建 (预计3-4天)
**目标**: 建立项目基础架构和核心服务

#### 1.1 项目环境配置
- [✅] 配置Android项目依赖
- [✅] 集成必要的第三方库
- [✅] 设置代码规范和工具

#### 1.2 核心服务实现
- [ ] AutomationAccessibilityService基础框架
- [✅] 权限管理系统
- [✅] 日志记录系统
- [✅] 基础UI界面

#### 1.3 数据存储层
- [✅] 注解驱动的脚本系统设计
- [✅] 脚本引擎架构实现
- [✅] 执行状态管理

### 第二阶段：脚本引擎开发 (已完成) ✅
**目标**: 实现注解驱动的脚本引擎系统

#### 2.1 注解系统设计
- [✅] @ScriptModel - 脚本基础信息注解
- [✅] @ScriptPage - 页面定义注解
- [✅] @ScriptIcons - 页面元素注解
- [✅] ScriptInfo数据模型

#### 2.2 脚本引擎核心
- [✅] ScriptEngine - 核心执行引擎
- [✅] PageRecognizer - 页面识别器
- [✅] AppLauncher - 应用启动器
- [✅] 脚本加载和解析机制

#### 2.3 示例脚本实现
- [✅] WeChatScript - 微信脚本示例
- [✅] KuaishouScript - 快手极速版脚本
- [✅] AlipayScript - 支付宝脚本
- [✅] 脚本注册和管理系统

### 第三阶段：应用策略实现 (预计5-6天)
**目标**: 实现各目标应用的自动化策略

#### 3.1 通用策略框架
- [ ] 应用识别和启动
- [ ] 页面状态分析
- [ ] 通用操作执行器
- [ ] 策略配置管理

#### 3.2 目标应用适配
- [ ] 根据实际需求实现具体应用的自动化流程
- [ ] 建立可扩展的策略插件系统
- [ ] 实现策略的动态加载和切换

### 第四阶段：防检测与优化 (预计3-4天)
**目标**: 提高操作的拟人化程度

#### 4.1 防检测机制
- [ ] 随机延迟系统
- [ ] 操作轨迹模拟
- [ ] 人性化行为添加
- [ ] 操作频率控制

#### 4.2 性能优化
- [ ] 内存管理优化
- [ ] 识别速度提升
- [ ] 电池消耗优化
- [ ] 异常处理完善

#### 4.3 用户体验
- [ ] 悬浮窗控制界面
- [ ] 任务状态显示
- [ ] 配置界面完善
- [ ] 操作日志查看

### 第五阶段：测试与完善 (预计2-3天)
**目标**: 全面测试和问题修复

#### 5.1 功能测试
- [ ] 各应用策略测试
- [ ] 多设备兼容性测试
- [ ] 长时间稳定性测试
- [ ] 异常情况处理测试

#### 5.2 性能测试
- [ ] 内存泄漏检测
- [ ] CPU使用率测试
- [ ] 电池消耗测试
- [ ] 响应速度测试

#### 5.3 用户体验测试
- [ ] 界面易用性测试
- [ ] 配置流程测试
- [ ] 错误提示优化

## 🔄 项目执行流程时序说明

### 整体执行流程图

系统启动后的完整执行时序：

```
用户启动应用 → 权限检查 → 启动无障碍服务 → 目标应用检测 → 
页面识别 → 策略选择 → 操作执行 → 结果验证 → 循环执行
```

### 详细执行时序

#### 阶段1：系统初始化 (0-5秒)
1. **应用启动**
   - 检查无障碍服务权限
   - 检查悬浮窗权限
   - 加载配置数据

2. **权限处理**
   - 如果权限不足 → 引导用户开启权限
   - 权限完整 → 进入主界面

3. **服务启动**
   - 启动AutomationAccessibilityService
   - 初始化识别引擎
   - 准备策略管理器

#### 阶段2：目标应用监控 (持续监控)
1. **应用检测循环** (每1秒)
   ```
   获取前台应用包名 → 判断是否为目标应用 → 
   是：进入自动化流程 | 否：继续监控
   ```

2. **应用启动处理**
   - 检测到目标应用启动
   - 等待应用完全加载 (2-5秒)
   - 开始页面分析

#### 阶段3：页面识别与分析 (2-8秒)
1. **多重识别流程**
   ```
   步骤1: AccessibilityService扫描 (100-500ms)
        ↓ (成功/失败)
   步骤2: 屏幕截图 + 图像识别 (500-2000ms)
        ↓ (成功/失败)  
   步骤3: OCR文字识别 (1000-3000ms)
        ↓ (成功/失败)
   步骤4: 智能区域推测 (100-300ms)
   ```

2. **页面状态判断**
   - 识别当前页面类型（视频页面/任务页面/广告页面等）
   - 确定可执行的操作列表
   - 选择最佳操作策略

#### 阶段4：策略执行 (3-15秒)
1. **操作前准备**
   - 生成随机延迟时间 (500-3000ms)
   - 计算目标点击位置
   - 添加随机偏移 (±5像素)

2. **操作执行**
   ```
   执行点击/滑动 → 等待响应 (1-3秒) → 验证操作结果 →
   成功：继续下一步 | 失败：重试/降级处理
   ```

3. **防检测处理**
   - 随机添加"误操作" (10%概率)
   - 模拟真实用户的犹豫行为
   - 动态调整操作频率

#### 阶段5：结果验证与循环 (1-3秒)
1. **操作结果检查**
   - 页面是否发生预期变化
   - 目标元素是否出现/消失
   - 判断操作是否成功

2. **异常处理**
   - 操作失败 → 重试机制 (最多3次)
   - 页面卡死 → 应用重启
   - 网络异常 → 等待重连

3. **循环控制**
   - 返回阶段2继续监控
   - 或进入下一个操作循环

### 典型应用场景时序示例

#### 快手极速版自动化时序

```mermaid
sequenceDiagram
    participant U as 用户
    participant S as SuperNuggets Master
    participant K as 快手极速版
    participant AS as 无障碍服务
    
    Note over U,AS: 系统初始化阶段
    U->>S: 启动应用
    S->>AS: 检查权限状态
    AS-->>S: 权限已开启
    S->>S: 初始化识别引擎
    
    Note over U,AS: 应用监控阶段
    loop 每1秒检测
        S->>AS: 获取前台应用包名
        AS-->>S: 应用包名信息
    end
    
    S->>K: 检测到快手极速版启动
    S->>S: 等待3秒(应用加载)
    
    Note over U,AS: 视频观看阶段
    S->>AS: 识别当前页面
    AS-->>S: 视频播放页面
    S->>S: 生成随机观看时长(30-120秒)
    
    loop 视频观看循环
        S->>K: 观看视频
        S->>S: 随机延迟(500-3000ms)
        S->>K: 随机点赞(30%概率)
        S->>S: 随机延迟(1000-2000ms)
        S->>K: 滑动到下一个视频
        S->>S: 等待视频加载(2秒)
    end
    
    Note over U,AS: 赚钱任务阶段
    S->>AS: 寻找"去赚钱"按钮
    AS-->>S: 找到按钮位置
    S->>K: 点击"去赚钱"
    S->>S: 等待页面跳转(3秒)
    
    S->>AS: 识别赚钱页面
    AS-->>S: 赚钱页面已加载
    S->>AS: 搜索广告任务
    AS-->>S: 找到"看广告得金币"
    S->>K: 点击广告任务
    
    Note over U,AS: 广告观看阶段
    S->>S: 等待广告页面加载(2秒)
    S->>AS: 监控广告播放状态
    AS-->>S: 广告正在播放
    S->>S: 等待广告完成(15-30秒)
    S->>AS: 检测"领取奖励"按钮
    AS-->>S: 按钮已出现
    S->>K: 点击领取奖励
    
    Note over U,AS: 循环重启
    S->>S: 等待3秒
    S->>K: 返回主页面
    S->>S: 重新开始监控循环
```

**详细步骤说明：**
1. 检测到快手极速版启动 (1秒)
2. 等待应用加载完成 (3秒)
3. 识别当前为视频播放页面 (2秒)
4. 随机观看视频 (30-120秒)
5. 随机点赞操作 (1秒)
6. 滑动到下一个视频 (2秒)
7. 重复3-6步骤 (3-5次)
8. 寻找"去赚钱"按钮 (3秒)
9. 点击进入赚钱页面 (2秒)
10. 识别广告任务 (3秒)
11. 点击观看广告 (1秒)
12. 等待广告播放完成 (15-30秒)
13. 领取奖励 (2秒)
14. 返回主页面，重新开始循环

#### 支付宝蚂蚁森林时序
```
1. 定时触发 (每天早上7点)
2. 启动支付宝 (3秒)
3. 搜索"蚂蚁森林"入口 (5秒)
4. 进入森林页面 (3秒)
5. 收集自己的能量 (2秒)
6. 进入好友列表 (2秒)
7. 循环访问好友森林 (每个好友3-5秒)
8. 收集好友能量
9. 完成后关闭支付宝
10. 任务结束，等待下次定时触发
```

### 性能时序要求

| 操作类型 | 预期响应时间 | 超时阈值 | 失败处理 |
|----------|--------------|----------|----------|
| 应用检测 | 100-500ms | 1秒 | 继续下一轮检测 |
| 页面识别 | 1-5秒 | 10秒 | 降级到备用识别方案 |
| 元素定位 | 500-2000ms | 5秒 | 使用预设位置 |
| 操作执行 | 100-300ms | 1秒 | 重试3次后跳过 |
| 页面跳转 | 1-3秒 | 8秒 | 强制刷新页面 |

### 异常处理时序

#### 应用崩溃处理
```
检测到目标应用消失 (1秒) → 等待5秒 → 尝试重新启动应用 → 
成功：继续自动化 | 失败：暂停30分钟后重试
```

#### 识别失败处理
```
第一次失败 → 等待2秒重试 → 第二次失败 → 降级识别方案 → 
仍失败 → 使用预设位置 → 最终失败 → 跳过当前操作
```

## 🛠️ 关键技术实现细节

### 多重识别引擎架构
```
识别优先级：
1. AccessibilityService (快速、准确)
2. 图像模板匹配 (Flutter/游戏/WebView)
3. OCR文字识别 (辅助方案)
4. 智能区域推测 (兜底方案)
```

### 防检测策略
```
时间随机化：500ms - 3000ms正态分布延迟
位置随机化：目标点周围±5像素随机偏移
行为多样化：随机添加"误操作"、"犹豫"等
频率控制：智能调节操作频率，避免过于规律
```

### 多分辨率适配
```
基准分辨率：1080x2340 (全面屏主流分辨率)
相对坐标系：所有位置使用0-1相对值
区域划分：9宫格功能区域划分
智能缩放：根据实际分辨率动态调整
异形屏适配：处理刘海屏、挖孔屏等特殊情况
```

## ⚠️ 风险评估与应对

### 技术风险
| 风险项 | 风险等级 | 应对措施 |
|--------|----------|----------|
| 目标应用更新导致识别失效 | 高 | 建立多重识别机制，提供快速适配方案 |
| 系统权限限制 | 中 | 详细的权限申请引导，提供替代方案 |
| 性能问题 | 中 | 持续性能监控，优化算法效率 |
| 兼容性问题 | 中 | 多设备测试，建立兼容性数据库 |

### 法律风险
| 风险项 | 风险等级 | 应对措施 |
|--------|----------|----------|
| 违反应用使用条款 | 高 | 明确声明仅供学习研究，添加使用条款 |
| 账号被封禁 | 中 | 提供免责声明，建议用户合理使用 |

## 📋 交付物清单

### 代码交付物
- [ ] 完整的Android项目源码
- [ ] 详细的代码注释和文档
- [ ] 单元测试用例
- [ ] 打包配置文件

### 文档交付物
- [ ] 用户使用说明文档
- [ ] 技术实现文档
- [ ] 部署和配置指南
- [ ] 常见问题解答

### 测试交付物
- [ ] 功能测试报告
- [ ] 性能测试报告
- [ ] 兼容性测试报告
- [ ] 已知问题清单

## 🔄 后续维护计划

### 短期维护 (1-3个月)
- 修复使用中发现的问题
- 适配目标应用的版本更新
- 添加用户反馈的功能需求

### 长期维护 (3-12个月)
- 支持新的目标应用
- 优化识别算法准确率
- 提升用户体验

## 📞 项目支持

- **技术支持**: 提供技术问题解答和bug修复
- **功能更新**: 根据需求添加新功能
- **文档维护**: 保持文档的准确性和完整性

---

**注意事项**：
1. 本项目仅供学习和研究使用
2. 请遵守相关应用的使用条款
3. 建议在开发和测试环境中使用
4. 对于账号安全问题，用户需自行承担责任

**项目预计总工期**: 16-21天
**核心开发工作量**: 约14-19天
**测试和完善工作**: 约2-3天

## 🔧 最新修复记录

### MediaProjection 截图系统完全重写 (2025-06-30)

#### 🐛 问题描述
应用在截图功能使用过程中频繁出现 `java.lang.SecurityException: Don't re-use the resultData to retrieve the same projection instance` 错误，导致图像识别功能无法正常工作。

#### 🔍 问题分析
经过深入调查发现，错误的根本原因是违反了Android对MediaProjection的安全要求：
1. **重复使用实例**: 当MediaProjection失效后，代码仍然尝试重新使用相同的实例
2. **VirtualDisplay重复创建**: 在同一个MediaProjection实例上多次创建VirtualDisplay
3. **复杂的状态管理**: 多个组件之间的MediaProjection实例管理混乱
4. **Token超时**: 使用已经超时的MediaProjection token

#### ⚡ 重写方案

##### 1. 第一次重写（部分解决）
- **删除旧代码**: 完全删除原有的ScreenshotHelper和MediaProjectionService
- **简化架构**: 采用"一次性使用"原则，每次截图都创建新的VirtualDisplay
- **统一管理**: MediaProjection实例只在ScreenshotHelper中管理
- **立即释放**: VirtualDisplay用完立即释放，避免资源泄漏

##### 2. 第二次重写（彻底解决）
发现第一次重写仍然存在MediaProjection实例重用问题，进行了更彻底的重写：

```kotlin
// 问题根源：Android不允许在同一个MediaProjection实例上多次创建VirtualDisplay
// 解决方案：每次截图都创建全新的MediaProjection实例

// 新的架构设计
class ScreenshotHelper {
    private var mediaProjectionProvider: (() -> MediaProjection?)? = null

    suspend fun captureScreen(): Bitmap? {
        // 每次都获取全新的MediaProjection实例
        val mediaProjection = mediaProjectionProvider?.invoke()

        // 使用完立即停止，确保不会重复使用
        try {
            return performSingleCapture(mediaProjection, screenInfo)
        } finally {
            mediaProjection?.stop() // 立即停止
        }
    }
}

class MediaProjectionHelper {
    private var permissionData: Intent? = null // 保存权限数据而不是实例

    private fun createNewMediaProjection(): MediaProjection? {
        // 每次都用权限数据创建新实例
        return manager.getMediaProjection(Activity.RESULT_OK, permissionData)
    }
}
```

##### 3. 最终的设计原则
- **权限数据保存**: 保存Intent权限数据而不是MediaProjection实例
- **按需创建**: 每次截图时创建全新的MediaProjection实例
- **立即销毁**: 使用完立即调用stop()，确保不会重复使用
- **提供者模式**: 使用回调函数提供MediaProjection实例

#### 📊 重写效果
- ✅ 完全消除了MediaProjection重复使用错误
- ✅ 大幅提升了截图功能的稳定性
- ✅ 简化了代码架构，减少了维护复杂性
- ✅ 改善了资源管理，避免内存泄漏
- ✅ 提升了用户体验（权限失效时明确提示）

#### 🔧 技术实现对比
```kotlin
// 原始版本：复杂的状态管理（问题源头）
class ScreenshotHelper {
    private var mediaProjection: MediaProjection? = null
    private var virtualDisplay: VirtualDisplay? = null // 长期持有

    fun captureScreen() {
        // 尝试重用VirtualDisplay（错误）
        if (virtualDisplay == null) {
            virtualDisplay = mediaProjection.createVirtualDisplay(...)
        }
    }
}

// 第一次重写：一次性VirtualDisplay（部分解决）
class ScreenshotHelper {
    private var currentMediaProjection: MediaProjection? = null

    suspend fun captureScreen(): Bitmap? {
        val mediaProjection = currentMediaProjection ?: return null

        // 每次都创建新的VirtualDisplay
        var virtualDisplay: VirtualDisplay? = null
        try {
            virtualDisplay = mediaProjection.createVirtualDisplay(...) // 仍然重用MediaProjection
            return waitForImage(imageReader)
        } finally {
            virtualDisplay?.release() // 立即释放
        }
    }
}

// 最终版本：每次创建新的MediaProjection（彻底解决）
class ScreenshotHelper {
    private var mediaProjectionProvider: (() -> MediaProjection?)? = null

    suspend fun captureScreen(): Bitmap? {
        // 每次都获取全新的MediaProjection实例
        val mediaProjection = mediaProjectionProvider?.invoke() ?: return null

        var virtualDisplay: VirtualDisplay? = null
        try {
            virtualDisplay = mediaProjection.createVirtualDisplay(...) // 全新实例
            return waitForImage(imageReader)
        } finally {
            virtualDisplay?.release()
            mediaProjection.stop() // 立即停止，确保不会重复使用
        }
    }
}
```

#### 🎯 重写覆盖范围
- `ScreenshotHelper.kt` - 完全重写，采用一次性VirtualDisplay策略
- `MediaProjectionService.kt` - 完全重写，简化为纯前台服务
- `MediaProjectionHelper.kt` - 大幅简化，移除复杂的状态管理
- `PermissionStatusMonitor.kt` - 移除不必要的MediaProjection检查

#### 🚀 性能优化
- **内存使用**: 每次截图后立即释放资源，避免内存累积
- **响应速度**: 简化的流程减少了等待时间
- **稳定性**: 消除了状态不一致导致的崩溃
- **兼容性**: 更好地遵循Android的MediaProjection使用规范

这次完全重写确保了截图功能的长期稳定运行，从根本上解决了MediaProjection相关的所有安全异常问题。