package com.google.android.accessibility.selecttospeak

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.GestureDescription
import android.content.Intent
import android.graphics.Path
import android.os.Bundle
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import com.jerome.supernuggetsmaster.utils.Logger
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit

/**
 * 系统无障碍服务 - SelectToSpeak伪装
 * 通过模拟系统内置的SelectToSpeak服务来绕过微信等应用的节点信息混淆
 *
 * 解决方案来源：
 * 微信8.0.52版本以后对AccessibilityService获取的节点信息进行了混淆
 * 通过创建与系统内置服务相同包名类名的服务可以绕过这个限制
 */
class SelectToSpeakService : AccessibilityService() {

    companion object {
        private var instance: SelectToSpeakService? = null

        /**
         * 获取服务实例
         */
        fun getInstance(): SelectToSpeakService? = instance

        /**
         * 检查服务是否运行
         */
        fun isRunning(): Boolean = instance != null
    }

    override fun onCreate() {
        super.onCreate()
        instance = this
        Logger.i("系统SelectToSpeak伪装服务创建")
    }

    override fun onDestroy() {
        super.onDestroy()
        instance = null
        Logger.i("系统SelectToSpeak伪装服务销毁")
    }

    override fun onServiceConnected() {
        super.onServiceConnected()
        Logger.i("系统SelectToSpeak伪装服务已连接")

        // 监听窗口变化事件以提供兼容性
        serviceInfo?.let { info ->
            info.eventTypes = info.eventTypes or
                    AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED or
                    AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED or
                    AccessibilityEvent.TYPE_VIEW_CLICKED
        }
    }

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        event?.let {
            when (it.eventType) {
                AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED -> {
//                    Logger.d("窗口状态变化: ${it.packageName}")
                    onWindowStateChanged(it)
                }

                AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED -> {
//                    Logger.d("窗口内容变化: ${it.packageName}")
                    onWindowContentChanged(it)
                }

                AccessibilityEvent.TYPE_VIEW_CLICKED -> {
//                    Logger.d("点击事件: ${it.packageName}")
                }
            }
        }
    }

    override fun onInterrupt() {
        Logger.w("系统SelectToSpeak伪装服务被中断")
    }

    /**
     * 窗口状态变化处理
     */
    private fun onWindowStateChanged(event: AccessibilityEvent) {
        val packageName = event.packageName?.toString()
        val className = event.className?.toString()

        if (packageName != null && className != null) {
            Logger.i("切换到应用: $packageName, 页面: $className")
        }
    }

    /**
     * 窗口内容变化处理
     */
    private fun onWindowContentChanged(event: AccessibilityEvent) {
        // 内容变化处理，暂时只记录
    }

    /**
     * 获取当前屏幕的根节点
     */
    fun getRootNode(): AccessibilityNodeInfo? {
        return try {
            rootInActiveWindow
        } catch (e: Exception) {
            Logger.e("获取根节点失败", e)
            null
        }
    }

    /**
     * 通过ID查找节点
     */
    fun findNodeById(id: String): List<AccessibilityNodeInfo?>? {
        return try {
            val rootNode = getRootNode()
            return rootNode?.findAccessibilityNodeInfosByViewId(id)
        } catch (e: Exception) {
            Logger.e("通过ID查找节点失败: $id", e)
            null
        }
    }

    /**
     * 通过文本查找节点
     */
    fun findNodeByText(text: String): AccessibilityNodeInfo? {
        return try {
            val rootNode = getRootNode()
            if (rootNode == null) {
                Logger.w("根节点为空，无法查找文本: $text")
                return null
            }

            Logger.d("开始查找文本: $text")
            val nodes = rootNode.findAccessibilityNodeInfosByText(text)

            Logger.d("找到 ${nodes?.size ?: 0} 个匹配的节点")

            if (nodes.isNullOrEmpty()) {
                Logger.w("未找到包含文本 '$text' 的节点")
                return null
            }

            // 检查每个找到的节点
            nodes.forEachIndexed { index, node ->
                Logger.d("节点[$index] - 文本: ${node.text}, 描述: ${node.contentDescription}, 类名: ${node.className}")
                Logger.d("节点[$index] - 可见: ${node.isVisibleToUser}, 可点击: ${node.isClickable}")
            }

            // 返回第一个节点
            nodes.firstOrNull()
        } catch (e: Exception) {
            Logger.e("通过文本查找节点失败: $text", e)
            null
        }
    }

    /**
     * 点击指定节点
     */
    fun clickNode(node: AccessibilityNodeInfo): Boolean {
        return try {
            Logger.d("准备点击节点 - 文本: ${node.text}, 描述: ${node.contentDescription}, 类名: ${node.className}")
            Logger.d("节点状态 - 可见: ${node.isVisibleToUser}, 可点击: ${node.isClickable}, 已启用: ${node.isEnabled}")

            // 获取节点的屏幕坐标
            val rect = android.graphics.Rect()
            node.getBoundsInScreen(rect)

            Logger.d("节点屏幕边界: $rect")

            // 检查边界是否有效
            if (rect.isEmpty || rect.width() <= 0 || rect.height() <= 0) {
                Logger.w("节点边界无效，尝试使用父节点")

                // 尝试找到有效的父节点
                var parent = node.parent
                var depth = 0
                while (parent != null && depth < 5) {
                    val parentRect = android.graphics.Rect()
                    parent.getBoundsInScreen(parentRect)

                    if (!parentRect.isEmpty && parentRect.width() > 0 && parentRect.height() > 0) {
                        Logger.d("使用父节点边界: $parentRect")
                        return clickByCoordinates(
                            parentRect.centerX().toFloat(),
                            parentRect.centerY().toFloat()
                        )
                    }

                    parent = parent.parent
                    depth++
                }

                Logger.e("无法找到有效的父节点进行点击")
                return false
            }

            // 计算节点矩形区域的中心点
            val centerX = rect.centerX().toFloat()
            val centerY = rect.centerY().toFloat()

            Logger.d("点击节点坐标: ($centerX, $centerY), 矩形区域: $rect")

            // 使用坐标点击的方式
            clickByCoordinates(centerX, centerY)
        } catch (e: Exception) {
            Logger.e("点击节点失败", e)
            false
        }
    }

    /**
     * 通过坐标点击
     */
    fun clickByCoordinates(x: Float, y: Float): Boolean {
        return try {
            Logger.d("准备点击坐标: ($x, $y)")

            val path = Path().apply { moveTo(x, y) }
            // 增加点击持续时间到300毫秒，模拟更真实的触摸
            val gesture = GestureDescription.Builder()
                .addStroke(GestureDescription.StrokeDescription(path, 0, 300))
                .build()

            val success = dispatchGesture(gesture, object : GestureResultCallback() {
                override fun onCompleted(gestureDescription: GestureDescription?) {
                    Logger.d("点击坐标完成: ($x, $y)")
                }

                override fun onCancelled(gestureDescription: GestureDescription?) {
                    Logger.w("点击坐标取消: ($x, $y)")
                }
            }, null)

            Logger.d("dispatchGesture返回结果: $success")

            if (success) {
                // 如果分发成功，稍微等待一下让手势执行
                try {
                    Thread.sleep(300)
                } catch (e: InterruptedException) {
                    // 忽略中断
                }
                Logger.d("手势已分发并等待执行完成")
            }

            success
        } catch (e: Exception) {
            Logger.e("通过坐标点击失败: ($x, $y)", e)
            false
        }
    }

    /**
     * 通过坐标长按
     * @param x 长按的X坐标
     * @param y 长按的Y坐标
     * @param durationMs 长按持续时间，默认1000毫秒
     */
    fun longClickByCoordinates(x: Float, y: Float, durationMs: Long = 1000): Boolean {
        return try {
            Logger.d("准备长按坐标: ($x, $y), 持续时间: ${durationMs}ms")

            val path = Path().apply { moveTo(x, y) }
            // 长按手势，持续指定时间
            val gesture = GestureDescription.Builder()
                .addStroke(GestureDescription.StrokeDescription(path, 0, durationMs))
                .build()

            val success = dispatchGesture(gesture, object : GestureResultCallback() {
                override fun onCompleted(gestureDescription: GestureDescription?) {
                    Logger.d("长按坐标完成: ($x, $y)")
                }

                override fun onCancelled(gestureDescription: GestureDescription?) {
                    Logger.w("长按坐标取消: ($x, $y)")
                }
            }, null)

            Logger.d("长按手势分发结果: $success")

            if (success) {
                // 等待长按手势执行完成
                try {
                    Thread.sleep(durationMs + 100) // 多等待100ms确保完成
                } catch (e: InterruptedException) {
                    // 忽略中断
                }
                Logger.d("长按手势已分发并等待执行完成")
            }

            success
        } catch (e: Exception) {
            Logger.e("通过坐标长按失败: ($x, $y)", e)
            false
        }
    }

    /**
     * 等待元素出现
     */
    fun waitForElement(text: String, timeoutMs: Long = 10000): AccessibilityNodeInfo? {
        val startTime = System.currentTimeMillis()
        val latch = CountDownLatch(1)
        var result: AccessibilityNodeInfo? = null

        val checkRunnable = object : Runnable {
            override fun run() {
                try {
                    result = findNodeByText(text)
                    if (result != null) {
                        latch.countDown()
                        return
                    }

                    val elapsed = System.currentTimeMillis() - startTime
                    if (elapsed < timeoutMs) {
                        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(this, 500)
                    } else {
                        latch.countDown()
                    }
                } catch (e: Exception) {
                    Logger.e("等待元素出现时发生异常", e)
                    latch.countDown()
                }
            }
        }

        // 开始检查
        checkRunnable.run()

        return try {
            latch.await(timeoutMs, TimeUnit.MILLISECONDS)
            result
        } catch (e: InterruptedException) {
            Logger.e("等待元素被中断", e)
            null
        }
    }

    /**
     * 滚动操作 - 只使用手势滑动
     */
    fun scroll(direction: ScrollDirection): Boolean {
        return performGestureScroll(direction)
    }

    /**
     * 使用手势执行滚动操作
     * 定义明确的起始坐标，避开状态栏和导航栏
     */
    fun performGestureScroll(direction: ScrollDirection): Boolean {
        return try {
            Logger.d("执行手势滚动: $direction")

            // 获取屏幕尺寸
            val displayMetrics = resources.displayMetrics
            val screenWidth = displayMetrics.widthPixels
            val screenHeight = displayMetrics.heightPixels

            Logger.d("屏幕尺寸: ${screenWidth}x${screenHeight}")

            // 定义安全的滑动区域，避开状态栏和导航栏
            val safeMarginTop = screenHeight * 0.15f    // 顶部留15%（状态栏、标题栏）
            val safeMarginBottom = screenHeight * 0.15f  // 底部留15%（导航栏、底部按钮）
            val safeMarginSide = screenWidth * 0.1f      // 左右留10%

            // 计算滑动的起始和结束坐标
            val startX = screenWidth / 2f  // 屏幕中心X坐标
            val startY: Float
            val endX: Float
            val endY: Float

            when (direction) {
                ScrollDirection.UP -> {
                    // 向上滑动：从屏幕下部往上滑
                    startY = screenHeight - safeMarginBottom
                    endX = startX
                    endY = safeMarginTop
                }
                ScrollDirection.DOWN -> {
                    // 向下滑动：从屏幕上部往下滑
                    startY = safeMarginTop
                    endX = startX
                    endY = screenHeight - safeMarginBottom
                }
                ScrollDirection.LEFT -> {
                    // 向左滑动：从屏幕右侧往左滑
                    startY = screenHeight / 2f
                    endX = safeMarginSide
                    endY = startY
                }
                ScrollDirection.RIGHT -> {
                    // 向右滑动：从屏幕左侧往右滑
                    startY = screenHeight / 2f
                    endX = screenWidth - safeMarginSide
                    endY = startY
                }
            }

            Logger.d("手势滑动坐标: 起始(${startX}, ${startY}) -> 结束(${endX}, ${endY})")

            // 创建滑动手势路径
            val path = Path()
            path.moveTo(startX, startY)
            path.lineTo(endX, endY)

            // 创建手势描述，使用600ms的滑动时间
            val gestureDescription = GestureDescription.Builder()
                .addStroke(GestureDescription.StrokeDescription(path, 0, 600))
                .build()

            // 执行手势
            val success = dispatchGesture(gestureDescription, object : GestureResultCallback() {
                override fun onCompleted(gestureDescription: GestureDescription?) {
                    Logger.d("手势滑动完成: $direction")
                }

                override fun onCancelled(gestureDescription: GestureDescription?) {
                    Logger.w("手势滑动取消: $direction")
                }
            }, null)

            if (success) {
                // 等待手势执行完成
                Thread.sleep(700)
                Logger.d("手势滑动执行成功")
            } else {
                Logger.w("手势滑动分发失败")
            }

            success
        } catch (e: Exception) {
            Logger.e("手势滑动异常: $direction", e)
            false
        }
    }



    enum class ScrollDirection {
        UP, DOWN, LEFT, RIGHT
    }
} 