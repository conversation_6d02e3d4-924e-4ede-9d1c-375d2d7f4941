package com.jerome.supernuggetsmaster.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.FragmentActivity
import com.jerome.supernuggetsmaster.permission.MediaProjectionManager as AppMediaProjectionManager
import com.jerome.supernuggetsmaster.service.MediaProjectionService
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 重新设计的媒体投影权限助手
 *
 * 职责：
 * 1. 处理媒体投影权限申请
 * 2. 保存权限数据，每次截图时创建新的MediaProjection实例
 * 3. 避免MediaProjection实例重用
 */
@Singleton
class MediaProjectionHelper @Inject constructor(
    @ApplicationContext private val context: Context,
    private val logger: Logger,
    private val screenshotHelper: ScreenshotHelper
) {

    private val manager = context.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager

    // 保存权限数据而不是MediaProjection实例
    @Volatile
    private var permissionData: Intent? = null

    /**
     * 请求媒体投影权限
     */
    fun requestPermission() {
        logger.i("MediaProjectionHelper", "开始请求媒体投影权限")
        val intent = manager.createScreenCaptureIntent()
        GlobalMediaProjectionLauncher.launch(intent)
    }

    /**
     * 处理权限请求结果
     */
    fun handlePermissionResult(resultCode: Int, data: Intent?) {
        logger.i("MediaProjectionHelper", "处理媒体投影权限申请结果: resultCode=$resultCode")

        if (resultCode == Activity.RESULT_OK && data != null) {
            try {
                logger.i("MediaProjectionHelper", "媒体投影权限申请成功，保存权限数据")

                // 保存权限数据
                permissionData = data

                // 先启动前台服务
                MediaProjectionService.start(context) {
                    // 服务启动完成后的回调
                    try {
                        logger.i("MediaProjectionHelper", "前台服务启动完成")

                        // 设置MediaProjection提供者到截图助手
                        screenshotHelper.setMediaProjectionProvider {
                            createNewMediaProjection()
                        }

                        // 更新权限状态
                        AppMediaProjectionManager.setInitialized(true)

                        logger.i("MediaProjectionHelper", "MediaProjection提供者设置完成")

                    } catch (e: Exception) {
                        logger.e("MediaProjectionHelper", "设置MediaProjection提供者时发生异常", e)
                        handlePermissionFailure()
                    }
                }

            } catch (e: Exception) {
                logger.e("MediaProjectionHelper", "处理媒体投影权限时发生异常", e)
                handlePermissionFailure()
            }
        } else {
            logger.w("MediaProjectionHelper", "媒体投影权限申请被拒绝")
            handlePermissionFailure()
        }
    }

    /**
     * 创建新的MediaProjection实例
     * 每次调用都返回全新的实例
     */
    private fun createNewMediaProjection(): MediaProjection? {
        return try {
            val data = permissionData
            if (data != null) {
                logger.d("MediaProjectionHelper", "创建新的MediaProjection实例")
                manager.getMediaProjection(Activity.RESULT_OK, data)
            } else {
                logger.w("MediaProjectionHelper", "权限数据为空，无法创建MediaProjection")
                null
            }
        } catch (e: Exception) {
            logger.e("MediaProjectionHelper", "创建MediaProjection时发生异常", e)
            null
        }
    }

    /**
     * 处理权限获取失败
     */
    private fun handlePermissionFailure() {
        permissionData = null
        AppMediaProjectionManager.setInitialized(false)
        MediaProjectionService.stop(context)
    }

    /**
     * 清理媒体投影资源
     */
    fun cleanup() {
        logger.i("MediaProjectionHelper", "清理媒体投影资源")

        try {
            // 清理权限数据
            permissionData = null

            // 清理ScreenshotHelper中的提供者
            screenshotHelper.clearMediaProjection()

            // 停止服务
            MediaProjectionService.stop(context)

            // 更新权限状态
            AppMediaProjectionManager.setInitialized(false)

        } catch (e: Exception) {
            logger.e("MediaProjectionHelper", "清理媒体投影资源时发生异常", e)
        }
    }

    /**
     * 检查MediaProjection是否可用
     */
    fun isMediaProjectionAvailable(): Boolean {
        return permissionData != null && AppMediaProjectionManager.isInitialized()
    }
}

/**
 * 全局媒体投影启动器
 */
object GlobalMediaProjectionLauncher {
    private var launcher: ActivityResultLauncher<Intent>? = null
    private var mediaProjectionHelper: MediaProjectionHelper? = null

    fun initialize(activity: FragmentActivity, helper: MediaProjectionHelper) {
        mediaProjectionHelper = helper
        launcher = activity.registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            mediaProjectionHelper?.handlePermissionResult(result.resultCode, result.data)
        }
    }

    fun launch(intent: Intent) {
        launcher?.launch(intent) ?: run {
            android.util.Log.e("GlobalMediaProjectionLauncher", "启动器未初始化")
        }
    }
}
