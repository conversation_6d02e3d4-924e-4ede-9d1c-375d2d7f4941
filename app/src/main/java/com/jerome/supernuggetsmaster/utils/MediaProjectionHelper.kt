package com.jerome.supernuggetsmaster.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.FragmentActivity
import com.jerome.supernuggetsmaster.permission.MediaProjectionManager as AppMediaProjectionManager
import com.jerome.supernuggetsmaster.permission.PermissionManager
import com.jerome.supernuggetsmaster.service.MediaProjectionService
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 媒体投影权限助手 - 服务配合版
 * 负责请求和管理媒体投影权限，使用前台服务满足Android要求
 */
@Singleton
class MediaProjectionHelper @Inject constructor(
    @ApplicationContext private val context: Context,
    private val logger: Logger,
    private val screenshotHelper: ScreenshotHelper,
    private val permissionManager: PermissionManager
) {

    private var mediaProjectionManager: MediaProjectionManager? = null
    private var mediaProjection: MediaProjection? = null
    
    /**
     * 初始化媒体投影管理器
     */
    fun initializeManager() {
        if (mediaProjectionManager == null) {
            mediaProjectionManager = context.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            logger.i("MediaProjectionHelper", "媒体投影管理器初始化完成")
        }
    }
    
    /**
     * 请求媒体投影权限
     */
    fun requestPermission() {
        initializeManager()
        val manager = mediaProjectionManager

        if (manager != null) {
            logger.i("MediaProjectionHelper", "开始请求媒体投影权限")
            val intent = manager.createScreenCaptureIntent()
            GlobalMediaProjectionLauncher.launch(intent)
        } else {
            logger.e("MediaProjectionHelper", "媒体投影管理器未初始化")
        }
    }
    
    /**
     * 处理权限请求结果 - 前台服务版
     */
    fun handlePermissionResult(data: Intent?) {
        if (data == null) {
            logger.w("MediaProjectionHelper", "媒体投影权限请求失败：数据为空")
            // 确保失败时清除状态
            AppMediaProjectionManager.setInitialized(false)
            return
        }

        try {
            val manager = mediaProjectionManager
            if (manager != null) {
                logger.i("MediaProjectionHelper", "启动媒体投影前台服务")
                
                // 先启动前台服务，然后在服务中创建MediaProjection
                MediaProjectionService.start(context, null) {
                    // 服务启动完成后的回调
                    try {
                        logger.i("MediaProjectionHelper", "前台服务启动完成，开始获取媒体投影")

                        // 在前台服务环境中获取媒体投影
                        mediaProjection = manager.getMediaProjection(Activity.RESULT_OK, data)

                        if (mediaProjection != null) {
                            logger.i("MediaProjectionHelper", "媒体投影权限获取成功")

                            // 更新服务中的媒体投影实例
                            MediaProjectionService.setMediaProjection(mediaProjection!!)

                            // 设置到截图助手（ScreenshotHelper会统一管理回调）
                            screenshotHelper.setMediaProjection(mediaProjection!!)

                            // 立即更新权限状态
                            AppMediaProjectionManager.setInitialized(true)
                            
                            // 重要：通知权限管理器立即更新状态
                            notifyPermissionStatusChanged()

                        } else {
                            logger.e("MediaProjectionHelper", "创建媒体投影失败")
                            AppMediaProjectionManager.setInitialized(false)
                            MediaProjectionService.stop(context)
                        }
                    } catch (e: Exception) {
                        logger.e("MediaProjectionHelper", "获取媒体投影时发生异常", e)
                        AppMediaProjectionManager.setInitialized(false)
                        MediaProjectionService.stop(context)
                    }
                }
            } else {
                logger.e("MediaProjectionHelper", "媒体投影管理器为空")
                AppMediaProjectionManager.setInitialized(false)
            }
        } catch (e: Exception) {
            logger.e("MediaProjectionHelper", "处理媒体投影权限结果时发生异常", e)
            AppMediaProjectionManager.setInitialized(false)
        }
    }
    
    /**
     * 通知权限状态发生变化
     * 触发PermissionManager重新检查权限状态
     */
    private fun notifyPermissionStatusChanged() {
        try {
            // 立即触发权限状态检查
            permissionManager.refreshPermissionStatus()
            logger.i("MediaProjectionHelper", "权限状态已更新并通知PermissionManager")
        } catch (e: Exception) {
            logger.e("MediaProjectionHelper", "通知权限状态变化时发生异常", e)
        }
    }
    
    /**
     * 检查是否已有权限
     */
    fun hasPermission(): Boolean {
        return MediaProjectionService.isRunning() &&
               AppMediaProjectionManager.isInitialized() && 
               screenshotHelper.isMediaProjectionAvailable()
    }

    /**
     * 重新创建 MediaProjection（如果需要）
     * 只在确认MediaProjection确实失效时才重置状态
     */
    fun refreshMediaProjectionIfNeeded() {
        // 只有在MediaProjection明确失效（例如被系统停止）时才重置状态
        // 避免频繁的状态检查导致误判
        if (AppMediaProjectionManager.isInitialized() && mediaProjection == null) {
            // 这种情况说明状态不一致，需要重置
            logger.w("MediaProjectionHelper", "检测到状态不一致，重置MediaProjection状态")
            AppMediaProjectionManager.setInitialized(false)
        }
        // 其他情况不做任何操作，保持状态稳定
    }

    /**
     * 强制刷新 MediaProjection
     * 当检测到 MediaProjection 失效时调用
     */
    fun forceRefreshMediaProjection() {
        logger.i("MediaProjectionHelper", "强制刷新 MediaProjection")

        // 彻底清除所有状态
        AppMediaProjectionManager.setInitialized(false)
        
        // 清理本地引用（不调用stop，避免与ScreenshotHelper的管理冲突）
        mediaProjection = null
        
        // 通知截图助手刷新（ScreenshotHelper会处理MediaProjection的停止和服务清理）
        screenshotHelper.forceRefresh()

        logger.w("MediaProjectionHelper", "MediaProjection 已彻底清除，需要用户重新授权")
    }
    
    /**
     * 释放媒体投影资源
     */
    fun release() {
        try {
            // 清理本地引用（不调用stop，避免重复释放）
            mediaProjection = null
            AppMediaProjectionManager.setInitialized(false)
            
            // 让ScreenshotHelper处理实际的资源释放
            screenshotHelper.release()
            
            logger.i("MediaProjectionHelper", "媒体投影资源已释放")
        } catch (e: Exception) {
            logger.e("MediaProjectionHelper", "释放媒体投影资源时发生异常", e)
        }
    }
    
    /**
     * 获取当前媒体投影实例
     */
    fun getMediaProjection(): MediaProjection? = mediaProjection

    /**
     * 检查MediaProjection是否需要重新授权
     * 当检测到MediaProjection失效时，不再尝试恢复，直接要求重新授权
     */
    fun checkMediaProjectionStatus(): Boolean {
        logger.i("MediaProjectionHelper", "检查MediaProjection状态")
        
        return try {
            // 如果MediaProjection为空或服务未运行，说明需要重新授权
            if (mediaProjection == null || !MediaProjectionService.isRunning()) {
                logger.w("MediaProjectionHelper", "MediaProjection不可用，需要重新授权")
                // 清理状态
                AppMediaProjectionManager.setInitialized(false)
                false
            } else {
                // MediaProjection存在且服务运行中
                logger.d("MediaProjectionHelper", "MediaProjection状态正常")
                true
            }
        } catch (e: Exception) {
            logger.e("MediaProjectionHelper", "检查MediaProjection状态时发生异常", e)
            // 发生异常时清理状态并要求重新授权
            AppMediaProjectionManager.setInitialized(false)
            false
        }
    }
}

/**
 * 全局媒体投影启动器 - 前台服务版
 */
object GlobalMediaProjectionLauncher {
    private var launcher: ActivityResultLauncher<Intent>? = null
    private var mediaProjectionHelper: MediaProjectionHelper? = null

    fun initialize(activity: FragmentActivity, helper: MediaProjectionHelper) {
        mediaProjectionHelper = helper
        launcher = activity.registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                mediaProjectionHelper?.handlePermissionResult(result.data)
            } else {
                android.util.Log.w("GlobalMediaProjectionLauncher", "用户拒绝了媒体投影权限")
                mediaProjectionHelper?.handlePermissionResult(null)
            }
        }
    }

    fun launch(intent: Intent) {
        launcher?.launch(intent) ?: run {
            android.util.Log.e("GlobalMediaProjectionLauncher", "启动器未初始化")
        }
    }
}
