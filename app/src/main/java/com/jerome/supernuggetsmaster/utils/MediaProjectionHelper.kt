package com.jerome.supernuggetsmaster.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.FragmentActivity
import com.jerome.supernuggetsmaster.permission.MediaProjectionManager as AppMediaProjectionManager
import com.jerome.supernuggetsmaster.service.MediaProjectionService
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 基于Service的媒体投影权限助手
 *
 * 职责：
 * 1. 处理媒体投影权限申请
 * 2. 启动MediaProjectionService并初始化MediaProjection
 * 3. 将MediaProjection管理完全交给Service
 */
@Singleton
class MediaProjectionHelper @Inject constructor(
    @ApplicationContext private val context: Context,
    private val logger: Logger,
    private val screenshotHelper: ScreenshotHelper
) {

    private val manager = context.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager

    /**
     * 请求媒体投影权限
     */
    fun requestPermission() {
        logger.i("MediaProjectionHelper", "开始请求媒体投影权限")
        val intent = manager.createScreenCaptureIntent()
        GlobalMediaProjectionLauncher.launch(intent)
    }

    /**
     * 处理权限请求结果
     */
    fun handlePermissionResult(resultCode: Int, data: Intent?) {
        logger.i("MediaProjectionHelper", "处理媒体投影权限申请结果: resultCode=$resultCode")

        if (resultCode == Activity.RESULT_OK && data != null) {
            try {
                logger.i("MediaProjectionHelper", "媒体投影权限申请成功，启动Service")

                // 先启动前台服务
                MediaProjectionService.start(context)

                // 等待一下让服务启动
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    try {
                        // 初始化Service中的MediaProjection
                        MediaProjectionService.initializeMediaProjection(context, resultCode, data)

                        // 更新权限状态
                        AppMediaProjectionManager.setInitialized(true)

                        logger.i("MediaProjectionHelper", "MediaProjection初始化完成")
                    } catch (e: Exception) {
                        logger.e("MediaProjectionHelper", "初始化MediaProjection时发生异常", e)
                        handlePermissionFailure()
                    }
                }, 500) // 500ms延迟

            } catch (e: Exception) {
                logger.e("MediaProjectionHelper", "处理媒体投影权限时发生异常", e)
                handlePermissionFailure()
            }
        } else {
            logger.w("MediaProjectionHelper", "媒体投影权限申请被拒绝")
            handlePermissionFailure()
        }
    }

    /**
     * 处理权限获取失败
     */
    private fun handlePermissionFailure() {
        AppMediaProjectionManager.setInitialized(false)
        MediaProjectionService.stop(context)
    }

    /**
     * 清理媒体投影资源
     */
    fun cleanup() {
        logger.i("MediaProjectionHelper", "清理媒体投影资源")

        try {
            // 清理ScreenshotHelper
            screenshotHelper.clearMediaProjection()

            // 停止服务
            MediaProjectionService.stop(context)

            // 更新权限状态
            AppMediaProjectionManager.setInitialized(false)

        } catch (e: Exception) {
            logger.e("MediaProjectionHelper", "清理媒体投影资源时发生异常", e)
        }
    }

    /**
     * 检查MediaProjection是否可用
     */
    fun isMediaProjectionAvailable(): Boolean {
        return MediaProjectionService.isRunning() && AppMediaProjectionManager.isInitialized()
    }
}

/**
 * 全局媒体投影启动器
 */
object GlobalMediaProjectionLauncher {
    private var launcher: ActivityResultLauncher<Intent>? = null
    private var mediaProjectionHelper: MediaProjectionHelper? = null

    fun initialize(activity: FragmentActivity, helper: MediaProjectionHelper) {
        mediaProjectionHelper = helper
        launcher = activity.registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            mediaProjectionHelper?.handlePermissionResult(result.resultCode, result.data)
        }
    }

    fun launch(intent: Intent) {
        launcher?.launch(intent) ?: run {
            android.util.Log.e("GlobalMediaProjectionLauncher", "启动器未初始化")
        }
    }
}
