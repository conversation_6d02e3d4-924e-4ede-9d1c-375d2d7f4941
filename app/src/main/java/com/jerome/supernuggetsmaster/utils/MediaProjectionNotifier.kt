package com.jerome.supernuggetsmaster.utils

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import com.jerome.supernuggetsmaster.MainActivity
import com.jerome.supernuggetsmaster.R
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * MediaProjection 状态通知器
 * 用于通知用户 MediaProjection 状态变化
 */
@Singleton
class MediaProjectionNotifier @Inject constructor(
    @ApplicationContext private val context: Context,
    private val logger: Logger
) {
    
    companion object {
        private const val CHANNEL_ID = "media_projection_status"
        private const val CHANNEL_NAME = "屏幕录制状态"
        private const val NOTIFICATION_ID = 2001
    }
    
    private val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
    
    init {
        createNotificationChannel()
    }
    
    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "屏幕录制权限状态通知"
                setShowBadge(true)
            }
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    /**
     * 通知用户 MediaProjection 已失效，需要重新授权
     */
    fun notifyMediaProjectionExpired() {
        logger.i("MediaProjectionNotifier", "发送 MediaProjection 失效通知")
        
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ID)
            .setContentTitle("屏幕录制权限已失效")
            .setContentText("请重新授权屏幕录制权限以继续使用图像识别功能")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .build()
        
        try {
            notificationManager.notify(NOTIFICATION_ID, notification)
        } catch (e: Exception) {
            logger.e("MediaProjectionNotifier", "发送通知失败", e)
        }
    }
    
    /**
     * 清除通知
     */
    fun clearNotification() {
        try {
            notificationManager.cancel(NOTIFICATION_ID)
        } catch (e: Exception) {
            logger.e("MediaProjectionNotifier", "清除通知失败", e)
        }
    }
}
