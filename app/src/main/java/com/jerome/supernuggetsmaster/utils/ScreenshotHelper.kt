package com.jerome.supernuggetsmaster.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.PixelFormat
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.Image
import android.media.ImageReader
import android.media.projection.MediaProjection
import android.os.Handler
import android.os.Looper
import android.util.DisplayMetrics
import android.view.WindowManager
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import com.jerome.supernuggetsmaster.service.MediaProjectionService
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume

/**
 * 独立管理的屏幕截图工具类
 * 
 * 设计原则：
 * 1. 完全独立管理MediaProjection，不依赖外部Service
 * 2. 每次截图使用一次性VirtualDisplay，用完即释放
 * 3. 简化架构，减少状态管理复杂性
 * 4. 当MediaProjection失效时，通知上层重新获取权限
 */
@Singleton
class ScreenshotHelper @Inject constructor(
    @ApplicationContext private val context: Context,
    private val logger: Logger,
    private val notifier: MediaProjectionNotifier
) {
    
    // MediaProjection实例，独立管理
    private var mediaProjection: MediaProjection? = null
    
    // 主线程Handler
    private val handler = Handler(Looper.getMainLooper())
    
    // 截图互斥锁，防止并发问题
    private val screenshotMutex = Mutex()
    
    // MediaProjection失效标志
    private var isMediaProjectionInvalid = false
    
    /**
     * 屏幕信息
     */
    data class ScreenInfo(
        val width: Int,
        val height: Int,
        val density: Float
    )
    
    /**
     * 获取屏幕信息
     */
    private fun getScreenInfo(): ScreenInfo {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val displayMetrics = DisplayMetrics()
        
        @Suppress("DEPRECATION")
        windowManager.defaultDisplay.getRealMetrics(displayMetrics)
        
        return ScreenInfo(
            width = displayMetrics.widthPixels,
            height = displayMetrics.heightPixels,
            density = displayMetrics.density
        )
    }
    
    /**
     * 设置MediaProjection实例
     * 由外部调用，传入用户授权后的MediaProjection
     */
    fun setMediaProjection(projection: MediaProjection) {
        logger.i("ScreenshotHelper", "设置新的MediaProjection实例")
        
        // 只清理本地的MediaProjection，不停止服务
        // 因为我们可能正在服务启动的回调中设置新实例
        try {
            if (mediaProjection != null && mediaProjection != projection) {
                mediaProjection?.stop()
                logger.d("ScreenshotHelper", "已清理旧的本地MediaProjection")
            }
        } catch (e: Exception) {
            logger.w("ScreenshotHelper", "清理旧MediaProjection时发生异常", e)
        }
        
        // 设置新的MediaProjection
        mediaProjection = projection
        isMediaProjectionInvalid = false
        
        // 设置停止回调
        projection.registerCallback(object : MediaProjection.Callback() {
            override fun onStop() {
                super.onStop()
                logger.w("ScreenshotHelper", "MediaProjection已停止")
                
                // 标记本地失效状态
                markMediaProjectionInvalid()
                
                // 更新全局权限状态
                com.jerome.supernuggetsmaster.permission.MediaProjectionManager.setInitialized(false)
                
                // 停止MediaProjection服务
                MediaProjectionService.stop(context)
                
                // 清理本地引用
                mediaProjection = null
                
                logger.i("ScreenshotHelper", "MediaProjection回调处理完成")
            }
        }, handler)
        
        logger.i("ScreenshotHelper", "MediaProjection设置完成")
    }
    
    /**
     * 检查MediaProjection是否可用
     */
    fun isMediaProjectionAvailable(): Boolean {
        if (mediaProjection == null || isMediaProjectionInvalid) {
            return false
        }
        
        // 简化状态检查：如果MediaProjection存在且没有被标记为失效，就认为可用
        // 避免频繁的VirtualDisplay测试导致状态抖动
        return true
    }
    
    /**
     * 获取屏幕截图 - 彻底修复版
     * 返回当前屏幕的Bitmap，当MediaProjection失效时不再尝试恢复，直接要求重新授权
     */
    suspend fun captureScreen(): Bitmap? = screenshotMutex.withLock {
        logger.d("ScreenshotHelper", "开始截图请求")

        // 获取有效的MediaProjection
        val projection = getValidMediaProjection()
        if (projection == null) {
            logger.e("ScreenshotHelper", "无法获取有效的MediaProjection，需要重新授权")
            notifyNeedPermission()
            return@withLock null
        }

        // 获取当前屏幕信息
        val screenInfo = getScreenInfo()
        logger.d("ScreenshotHelper", "准备截图，屏幕尺寸: ${screenInfo.width}x${screenInfo.height}")
        
        // 执行截图
        val result = performOneShotCapture(projection, screenInfo)
        
        // 如果截图失败，直接通知需要重新授权，不再尝试恢复
        if (result == null) {
            logger.e("ScreenshotHelper", "截图失败，需要重新获取权限")
            // 清理所有MediaProjection实例
            clearAllMediaProjections()
            notifyNeedPermission()
        }
        
        return@withLock result
    }
    
    /**
     * 获取有效的MediaProjection
     * 优先从服务获取，然后是本地实例，包含状态验证
     */
    private fun getValidMediaProjection(): MediaProjection? {
        // 首先尝试从服务获取MediaProjection
        val serviceProjection = MediaProjectionService.getMediaProjection()
        if (serviceProjection != null) {
            // 验证服务中的MediaProjection是否仍然有效
            if (isMediaProjectionCurrent(serviceProjection)) {
                logger.d("ScreenshotHelper", "从服务获取到有效的MediaProjection")
                return serviceProjection
            } else {
                logger.w("ScreenshotHelper", "服务中的MediaProjection已失效")
                // 清理失效的MediaProjection
                clearAllMediaProjections()
                return null
            }
        }
        
        // 检查本地的MediaProjection
        if (mediaProjection != null && !isMediaProjectionInvalid) {
            if (isMediaProjectionCurrent(mediaProjection!!)) {
                logger.d("ScreenshotHelper", "使用本地有效的MediaProjection")
                return mediaProjection
            } else {
                logger.w("ScreenshotHelper", "本地MediaProjection已失效")
                clearAllMediaProjections()
                return null
            }
        }
        
        logger.w("ScreenshotHelper", "无法获取有效的MediaProjection")
        return null
    }
    
    /**
     * 检查MediaProjection是否为当前有效状态
     * 通过尝试创建一个极小的测试VirtualDisplay来验证
     */
    private fun isMediaProjectionCurrent(projection: MediaProjection): Boolean {
        return try {
            // 创建一个1x1像素的测试显示来验证MediaProjection是否为current
            val testReader = ImageReader.newInstance(1, 1, PixelFormat.RGBA_8888, 1)
            val testDisplay = projection.createVirtualDisplay(
                "TestCurrent-${System.currentTimeMillis()}",
                1, 1, 160,
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                testReader.surface,
                null, null
            )
            
            // 如果能成功创建，说明MediaProjection是current的
            testDisplay.release()
            testReader.close()
            
            logger.d("ScreenshotHelper", "MediaProjection状态检查：当前有效")
            true
        } catch (e: SecurityException) {
            if (e.message?.contains("non-current") == true || 
                e.message?.contains("Don't re-use") == true ||
                e.message?.contains("timed out") == true) {
                logger.w("ScreenshotHelper", "MediaProjection已失效：${e.message}")
            } else {
                logger.w("ScreenshotHelper", "MediaProjection状态检查失败：${e.message}")
            }
            false
        } catch (e: Exception) {
            logger.w("ScreenshotHelper", "MediaProjection状态检查异常", e)
            false
        }
    }
    
    /**
     * 执行一次性截图
     * 每次创建新的VirtualDisplay和ImageReader，用完即释放
     */
    private suspend fun performOneShotCapture(
        projection: MediaProjection,
        screenInfo: ScreenInfo
    ): Bitmap? {
        var imageReader: ImageReader? = null
        var virtualDisplay: VirtualDisplay? = null
        
        return try {
            // 创建ImageReader
            imageReader = ImageReader.newInstance(
                screenInfo.width,
                screenInfo.height,
                PixelFormat.RGBA_8888,
                1
            )
            
            // 创建VirtualDisplay - 使用唯一名称避免冲突
            val displayName = "Screenshot-${System.currentTimeMillis()}-${Thread.currentThread().id}"
            virtualDisplay = projection.createVirtualDisplay(
                displayName,
                screenInfo.width,
                screenInfo.height,
                (screenInfo.density * 160).toInt(),
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                imageReader.surface,
                null,
                handler
            )
            
            logger.d("ScreenshotHelper", "VirtualDisplay创建成功: $displayName")
            
            // 等待图像可用并获取截图
            val bitmap = waitForImage(imageReader)
            
            if (bitmap != null) {
                logger.d("ScreenshotHelper", "截图成功")
            } else {
                logger.w("ScreenshotHelper", "截图失败，未获取到图像")
            }
            
            bitmap
            
        } catch (e: SecurityException) {
            logger.e("ScreenshotHelper", "截图时发生安全异常，MediaProjection已失效", e)
            // 立即标记失效，不再尝试使用
            markMediaProjectionInvalid()
            null
        } catch (e: Exception) {
            logger.e("ScreenshotHelper", "截图过程中发生异常", e)
            null
        } finally {
            // 确保资源被释放
            try {
                virtualDisplay?.release()
                imageReader?.close()
                logger.d("ScreenshotHelper", "截图资源已释放")
            } catch (e: Exception) {
                logger.w("ScreenshotHelper", "释放截图资源时发生异常", e)
            }
        }
    }
    
    /**
     * 等待图像可用
     */
    private suspend fun waitForImage(imageReader: ImageReader): Bitmap? {
        return suspendCancellableCoroutine { continuation ->
            var completed = false
            
            // 设置图像可用监听器
            imageReader.setOnImageAvailableListener({ reader ->
                if (!completed) {
                    completed = true
                    try {
                        val image = reader.acquireLatestImage()
                        if (image != null) {
                            val bitmap = convertImageToBitmap(image)
                            image.close()
                            continuation.resume(bitmap)
                        } else {
                            logger.w("ScreenshotHelper", "获取的图像为空")
                            continuation.resume(null)
                        }
                    } catch (e: Exception) {
                        logger.e("ScreenshotHelper", "处理图像时发生异常", e)
                        continuation.resume(null)
                    }
                }
            }, handler)
            
            // 设置取消回调
            continuation.invokeOnCancellation {
                if (!completed) {
                    completed = true
                    logger.d("ScreenshotHelper", "截图操作被取消")
                }
            }
            
            // 设置超时
            handler.postDelayed({
                if (!completed) {
                    completed = true
                    logger.w("ScreenshotHelper", "截图超时")
                    continuation.resume(null)
                }
            }, 3000) // 3秒超时，比之前短一些
        }
    }
    
    /**
     * 将Image转换为Bitmap
     */
    private fun convertImageToBitmap(image: Image): Bitmap? {
        return try {
            val planes = image.planes
            val buffer = planes[0].buffer
            val pixelStride = planes[0].pixelStride
            val rowStride = planes[0].rowStride
            val rowPadding = rowStride - pixelStride * image.width
            
            // 创建Bitmap
            val bitmap = Bitmap.createBitmap(
                image.width + rowPadding / pixelStride,
                image.height,
                Bitmap.Config.ARGB_8888
            )
            bitmap.copyPixelsFromBuffer(buffer)
            
            // 如果有行填充，需要裁剪到正确尺寸
            if (rowPadding > 0) {
                Bitmap.createBitmap(bitmap, 0, 0, image.width, image.height)
            } else {
                bitmap
            }
        } catch (e: Exception) {
            logger.e("ScreenshotHelper", "Image转Bitmap失败", e)
            null
        }
    }
    
    /**
     * 标记MediaProjection为失效状态
     */
    private fun markMediaProjectionInvalid() {
        isMediaProjectionInvalid = true
        logger.w("ScreenshotHelper", "MediaProjection已标记为失效")
    }
    
    /**
     * 清理所有MediaProjection实例
     */
    private fun clearAllMediaProjections() {
        try {
            // 清理本地MediaProjection
            mediaProjection?.stop()
            mediaProjection = null
            isMediaProjectionInvalid = true
            
            // 停止MediaProjection服务，这会清理服务中的实例
            MediaProjectionService.stop(context)
            
            logger.i("ScreenshotHelper", "所有MediaProjection实例已清理")
        } catch (e: Exception) {
            logger.w("ScreenshotHelper", "清理MediaProjection时发生异常", e)
        }
    }
    
    /**
     * 通知需要重新获取权限
     */
    private fun notifyNeedPermission() {
        try {
            // 发送通知给用户
            notifier.notifyMediaProjectionExpired()
            
            // 更新全局权限状态
            com.jerome.supernuggetsmaster.permission.MediaProjectionManager.setInitialized(false)
            
            logger.i("ScreenshotHelper", "已通知需要重新获取MediaProjection权限")
        } catch (e: Exception) {
            logger.e("ScreenshotHelper", "通知权限失效时发生异常", e)
        }
    }
    
    /**
     * 清理MediaProjection
     */
    private fun clearMediaProjection() {
        clearAllMediaProjections()
    }
    
    /**
     * 释放所有资源
     */
    fun release() {
        logger.i("ScreenshotHelper", "释放所有资源")
        clearMediaProjection()
    }
    
    /**
     * 强制刷新MediaProjection状态
     * 当外部检测到问题时可以调用
     */
    fun forceRefresh() {
        logger.i("ScreenshotHelper", "强制刷新MediaProjection状态")
        markMediaProjectionInvalid()
        notifyNeedPermission()
    }
}

