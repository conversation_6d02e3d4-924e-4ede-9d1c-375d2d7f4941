package com.jerome.supernuggetsmaster.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.PixelFormat
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.Image
import android.media.ImageReader
import android.media.projection.MediaProjection
import android.os.Handler
import android.os.Looper
import android.util.DisplayMetrics
import android.view.WindowManager
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume

/**
 * 全新的屏幕截图工具类
 * 
 * 设计原则：
 * 1. 每次截图都使用全新的MediaProjection实例
 * 2. 一次性使用VirtualDisplay，用完立即释放
 * 3. 简化状态管理，避免实例重用
 * 4. 当截图失败时，直接通知需要重新授权
 */
@Singleton
class ScreenshotHelper @Inject constructor(
    @ApplicationContext private val context: Context,
    private val logger: Logger
) {
    
    // 截图互斥锁，防止并发问题
    private val screenshotMutex = Mutex()
    
    // 主线程Handler
    private val handler = Handler(Looper.getMainLooper())
    
    // 当前的MediaProjection实例（每次使用后立即清理）
    @Volatile
    private var currentMediaProjection: MediaProjection? = null
    
    /**
     * 屏幕信息
     */
    data class ScreenInfo(
        val width: Int,
        val height: Int,
        val density: Float
    )
    
    /**
     * 设置MediaProjection实例
     * 注意：每次设置都会清理之前的实例
     */
    fun setMediaProjection(mediaProjection: MediaProjection) {
        logger.i("ScreenshotHelper", "设置新的MediaProjection实例")
        
        // 清理之前的实例
        clearMediaProjection()
        
        // 设置新实例
        currentMediaProjection = mediaProjection
        
        // 设置停止回调
        mediaProjection.registerCallback(object : MediaProjection.Callback() {
            override fun onStop() {
                super.onStop()
                logger.w("ScreenshotHelper", "MediaProjection已停止")
                clearMediaProjection()
            }
        }, handler)
        
        logger.i("ScreenshotHelper", "MediaProjection设置完成")
    }
    
    /**
     * 清理MediaProjection实例
     */
    fun clearMediaProjection() {
        try {
            currentMediaProjection?.stop()
            currentMediaProjection = null
            logger.d("ScreenshotHelper", "MediaProjection已清理")
        } catch (e: Exception) {
            logger.w("ScreenshotHelper", "清理MediaProjection时发生异常", e)
        }
    }
    
    /**
     * 获取屏幕截图
     * 返回当前屏幕的Bitmap，失败时返回null
     */
    suspend fun captureScreen(): Bitmap? = screenshotMutex.withLock {
        logger.d("ScreenshotHelper", "开始截图请求")
        
        val mediaProjection = currentMediaProjection
        if (mediaProjection == null) {
            logger.e("ScreenshotHelper", "MediaProjection未设置，无法截图")
            return@withLock null
        }
        
        // 获取屏幕信息
        val screenInfo = getScreenInfo()
        logger.d("ScreenshotHelper", "屏幕尺寸: ${screenInfo.width}x${screenInfo.height}")
        
        // 执行一次性截图
        val result = performSingleCapture(mediaProjection, screenInfo)
        
        if (result == null) {
            logger.e("ScreenshotHelper", "截图失败")
            // 截图失败时清理MediaProjection，强制重新授权
            clearMediaProjection()
        } else {
            logger.d("ScreenshotHelper", "截图成功")
        }
        
        return@withLock result
    }
    
    /**
     * 获取屏幕信息
     */
    private fun getScreenInfo(): ScreenInfo {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val displayMetrics = DisplayMetrics()
        windowManager.defaultDisplay.getRealMetrics(displayMetrics)
        
        return ScreenInfo(
            width = displayMetrics.widthPixels,
            height = displayMetrics.heightPixels,
            density = displayMetrics.density
        )
    }
    
    /**
     * 执行一次性截图
     * 创建VirtualDisplay，获取图像，然后立即释放所有资源
     */
    private suspend fun performSingleCapture(
        mediaProjection: MediaProjection,
        screenInfo: ScreenInfo
    ): Bitmap? = withContext(Dispatchers.Main) {
        
        var imageReader: ImageReader? = null
        var virtualDisplay: VirtualDisplay? = null
        
        return@withContext try {
            logger.d("ScreenshotHelper", "开始创建截图资源")
            
            // 创建ImageReader
            imageReader = ImageReader.newInstance(
                screenInfo.width,
                screenInfo.height,
                PixelFormat.RGBA_8888,
                1
            )
            
            // 创建VirtualDisplay - 使用唯一名称
            val displayName = "Screenshot-${System.currentTimeMillis()}"
            virtualDisplay = mediaProjection.createVirtualDisplay(
                displayName,
                screenInfo.width,
                screenInfo.height,
                (screenInfo.density * 160).toInt(),
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                imageReader.surface,
                null,
                handler
            )
            
            logger.d("ScreenshotHelper", "VirtualDisplay创建成功: $displayName")
            
            // 等待图像可用并获取截图
            val bitmap = waitForImage(imageReader)
            
            if (bitmap != null) {
                logger.d("ScreenshotHelper", "截图获取成功")
            } else {
                logger.w("ScreenshotHelper", "截图获取失败")
            }
            
            bitmap
            
        } catch (e: SecurityException) {
            logger.e("ScreenshotHelper", "截图时发生安全异常", e)
            null
        } catch (e: Exception) {
            logger.e("ScreenshotHelper", "截图过程中发生异常", e)
            null
        } finally {
            // 确保资源被释放
            try {
                virtualDisplay?.release()
                imageReader?.close()
                logger.d("ScreenshotHelper", "截图资源已释放")
            } catch (e: Exception) {
                logger.w("ScreenshotHelper", "释放截图资源时发生异常", e)
            }
        }
    }
    
    /**
     * 等待ImageReader获取图像
     */
    private suspend fun waitForImage(imageReader: ImageReader): Bitmap? {
        return withTimeoutOrNull(5000) { // 5秒超时
            suspendCancellableCoroutine { continuation ->
                val listener = ImageReader.OnImageAvailableListener { reader ->
                    try {
                        val image = reader.acquireLatestImage()
                        if (image != null) {
                            val bitmap = imageToBitmap(image)
                            image.close()
                            if (continuation.isActive) {
                                continuation.resume(bitmap)
                            }
                        } else {
                            if (continuation.isActive) {
                                continuation.resume(null)
                            }
                        }
                    } catch (e: Exception) {
                        logger.e("ScreenshotHelper", "处理图像时发生异常", e)
                        if (continuation.isActive) {
                            continuation.resume(null)
                        }
                    }
                }
                
                imageReader.setOnImageAvailableListener(listener, handler)
                
                continuation.invokeOnCancellation {
                    imageReader.setOnImageAvailableListener(null, null)
                }
            }
        }
    }
    
    /**
     * 将Image转换为Bitmap
     */
    private fun imageToBitmap(image: Image): Bitmap? {
        return try {
            val planes = image.planes
            val buffer = planes[0].buffer
            val pixelStride = planes[0].pixelStride
            val rowStride = planes[0].rowStride
            val rowPadding = rowStride - pixelStride * image.width
            
            val bitmap = Bitmap.createBitmap(
                image.width + rowPadding / pixelStride,
                image.height,
                Bitmap.Config.ARGB_8888
            )
            bitmap.copyPixelsFromBuffer(buffer)
            
            // 如果有padding，需要裁剪
            if (rowPadding != 0) {
                Bitmap.createBitmap(bitmap, 0, 0, image.width, image.height)
            } else {
                bitmap
            }
        } catch (e: Exception) {
            logger.e("ScreenshotHelper", "转换图像为Bitmap时发生异常", e)
            null
        }
    }
    
    /**
     * 检查MediaProjection是否可用
     */
    fun isMediaProjectionAvailable(): Boolean {
        return currentMediaProjection != null
    }
}
