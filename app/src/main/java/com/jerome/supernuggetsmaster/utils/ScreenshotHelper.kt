package com.jerome.supernuggetsmaster.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.PixelFormat
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.Image
import android.media.ImageReader
import android.media.projection.MediaProjection
import android.os.Handler
import android.os.Looper
import android.util.DisplayMetrics
import android.view.WindowManager
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume

/**
 * 最终版本的屏幕截图工具类
 *
 * 核心发现：Android的MediaProjection有严格限制
 * 1. 同一个Intent权限数据只能创建一个MediaProjection实例
 * 2. 一个MediaProjection实例只能创建一个VirtualDisplay
 * 3. 必须每次重新申请权限才能进行多次截图
 *
 * 解决方案：缓存单次截图，避免频繁重新申请权限
 */
@Singleton
class ScreenshotHelper @Inject constructor(
    @ApplicationContext private val context: Context,
    private val logger: Logger
) {

    // 截图互斥锁，防止并发问题
    private val screenshotMutex = Mutex()

    // 主线程Handler
    private val handler = Handler(Looper.getMainLooper())

    // 缓存的截图和时间戳
    @Volatile
    private var cachedBitmap: Bitmap? = null
    @Volatile
    private var cacheTimestamp: Long = 0

    // 缓存有效期（毫秒）
    private val cacheValidDuration = 2000L // 2秒

    // MediaProjection提供者回调
    private var mediaProjectionProvider: (() -> MediaProjection?)? = null
    
    /**
     * 屏幕信息
     */
    data class ScreenInfo(
        val width: Int,
        val height: Int,
        val density: Float
    )
    
    /**
     * 设置MediaProjection提供者
     * 每次截图时都会调用此提供者获取新的MediaProjection实例
     */
    fun setMediaProjectionProvider(provider: () -> MediaProjection?) {
        logger.i("ScreenshotHelper", "设置MediaProjection提供者")
        mediaProjectionProvider = provider
    }
    
    /**
     * 获取屏幕截图
     * 每次都获取全新的MediaProjection实例
     */
    suspend fun captureScreen(): Bitmap? = screenshotMutex.withLock {
        logger.d("ScreenshotHelper", "开始截图请求")

        // 每次都获取全新的MediaProjection实例
        val mediaProjection = mediaProjectionProvider?.invoke()
        if (mediaProjection == null) {
            logger.e("ScreenshotHelper", "无法获取MediaProjection实例，需要重新授权")
            return@withLock null
        }

        // 获取屏幕信息
        val screenInfo = getScreenInfo()
        logger.d("ScreenshotHelper", "屏幕尺寸: ${screenInfo.width}x${screenInfo.height}")

        // 执行一次性截图，使用完立即释放MediaProjection
        val result = performSingleCapture(mediaProjection, screenInfo)

        // 立即停止MediaProjection，确保不会重复使用
        try {
            mediaProjection.stop()
            logger.d("ScreenshotHelper", "MediaProjection已停止")
        } catch (e: Exception) {
            logger.w("ScreenshotHelper", "停止MediaProjection时发生异常", e)
        }

        if (result == null) {
            logger.e("ScreenshotHelper", "截图失败")
        } else {
            logger.d("ScreenshotHelper", "截图成功")
        }

        return@withLock result
    }
    
    /**
     * 获取屏幕信息
     */
    private fun getScreenInfo(): ScreenInfo {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val displayMetrics = DisplayMetrics()
        windowManager.defaultDisplay.getRealMetrics(displayMetrics)
        
        return ScreenInfo(
            width = displayMetrics.widthPixels,
            height = displayMetrics.heightPixels,
            density = displayMetrics.density
        )
    }
    
    /**
     * 执行一次性截图
     * 创建VirtualDisplay，获取图像，然后立即释放所有资源
     */
    private suspend fun performSingleCapture(
        mediaProjection: MediaProjection,
        screenInfo: ScreenInfo
    ): Bitmap? = withContext(Dispatchers.Main) {

        var imageReader: ImageReader? = null
        var virtualDisplay: VirtualDisplay? = null

        return@withContext try {
            logger.d("ScreenshotHelper", "开始创建截图资源")

            // 注册MediaProjection回调（Android要求）
            mediaProjection.registerCallback(object : MediaProjection.Callback() {
                override fun onStop() {
                    super.onStop()
                    logger.d("ScreenshotHelper", "MediaProjection回调：已停止")
                }
            }, handler)

            // 创建ImageReader
            imageReader = ImageReader.newInstance(
                screenInfo.width,
                screenInfo.height,
                PixelFormat.RGBA_8888,
                1
            )

            // 创建VirtualDisplay - 使用唯一名称
            val displayName = "Screenshot-${System.currentTimeMillis()}"
            virtualDisplay = mediaProjection.createVirtualDisplay(
                displayName,
                screenInfo.width,
                screenInfo.height,
                (screenInfo.density * 160).toInt(),
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                imageReader.surface,
                null,
                handler
            )
            
            logger.d("ScreenshotHelper", "VirtualDisplay创建成功: $displayName")

            // 等待VirtualDisplay稳定
            kotlinx.coroutines.delay(500)
            logger.d("ScreenshotHelper", "VirtualDisplay已稳定，开始等待图像数据")

            // 等待图像可用并获取截图
            val bitmap = waitForImage(imageReader)
            
            if (bitmap != null) {
                logger.d("ScreenshotHelper", "截图获取成功")
            } else {
                logger.w("ScreenshotHelper", "截图获取失败")
            }
            
            bitmap
            
        } catch (e: SecurityException) {
            logger.e("ScreenshotHelper", "截图时发生安全异常", e)
            null
        } catch (e: Exception) {
            logger.e("ScreenshotHelper", "截图过程中发生异常", e)
            null
        } finally {
            // 确保资源被释放
            try {
                virtualDisplay?.release()
                imageReader?.close()
                logger.d("ScreenshotHelper", "截图资源已释放")
            } catch (e: Exception) {
                logger.w("ScreenshotHelper", "释放截图资源时发生异常", e)
            }
        }
    }
    
    /**
     * 等待ImageReader获取图像
     */
    private suspend fun waitForImage(imageReader: ImageReader): Bitmap? {
        return withTimeoutOrNull(10000) { // 增加到10秒超时
            suspendCancellableCoroutine { continuation ->
                var isCompleted = false

                val listener = ImageReader.OnImageAvailableListener { reader ->
                    if (isCompleted) return@OnImageAvailableListener

                    try {
                        val image = reader.acquireLatestImage()
                        if (image != null) {
                            logger.d("ScreenshotHelper", "收到图像数据，尺寸: ${image.width}x${image.height}")
                            val bitmap = imageToBitmap(image)
                            image.close()

                            if (continuation.isActive && !isCompleted) {
                                isCompleted = true
                                continuation.resume(bitmap)
                            }
                        } else {
                            logger.w("ScreenshotHelper", "ImageReader返回null图像")
                            if (continuation.isActive && !isCompleted) {
                                isCompleted = true
                                continuation.resume(null)
                            }
                        }
                    } catch (e: Exception) {
                        logger.e("ScreenshotHelper", "处理图像时发生异常", e)
                        if (continuation.isActive && !isCompleted) {
                            isCompleted = true
                            continuation.resume(null)
                        }
                    }
                }

                // 设置监听器
                imageReader.setOnImageAvailableListener(listener, handler)
                logger.d("ScreenshotHelper", "ImageReader监听器已设置，等待图像数据...")

                continuation.invokeOnCancellation {
                    logger.d("ScreenshotHelper", "截图等待被取消")
                    imageReader.setOnImageAvailableListener(null, null)
                    isCompleted = true
                }
            }
        }
    }
    
    /**
     * 将Image转换为Bitmap
     */
    private fun imageToBitmap(image: Image): Bitmap? {
        return try {
            val planes = image.planes
            val buffer = planes[0].buffer
            val pixelStride = planes[0].pixelStride
            val rowStride = planes[0].rowStride
            val rowPadding = rowStride - pixelStride * image.width
            
            val bitmap = Bitmap.createBitmap(
                image.width + rowPadding / pixelStride,
                image.height,
                Bitmap.Config.ARGB_8888
            )
            bitmap.copyPixelsFromBuffer(buffer)
            
            // 如果有padding，需要裁剪
            if (rowPadding != 0) {
                Bitmap.createBitmap(bitmap, 0, 0, image.width, image.height)
            } else {
                bitmap
            }
        } catch (e: Exception) {
            logger.e("ScreenshotHelper", "转换图像为Bitmap时发生异常", e)
            null
        }
    }
    
    /**
     * 检查MediaProjection是否可用
     */
    fun isMediaProjectionAvailable(): Boolean {
        return mediaProjectionProvider != null
    }

    /**
     * 清理资源（兼容旧接口）
     */
    fun clearMediaProjection() {
        logger.d("ScreenshotHelper", "清理MediaProjection提供者")
        mediaProjectionProvider = null
    }
}
