package com.jerome.supernuggetsmaster.scripts

import com.jerome.supernuggetsmaster.annotation.ScriptModel
import com.jerome.supernuggetsmaster.annotation.ScriptPage
import com.jerome.supernuggetsmaster.annotation.ScriptIcon
import com.jerome.supernuggetsmaster.data.ScriptIconInfo

@ScriptModel(
    id = "kuaishou",
    name = "快手极速版",
    description = "自动观看视频、获取金币奖励",
    targetApp = "com.kuaishou.nebula",
    icon = "⚡",
    estimatedDuration = 300
)
class KuaishouScript {
    
    @ScriptPage(
        name = "首页视频",
        ids = ["com.kuaishou.nebula:id/main_container", "com.kuaishou.nebula:id/video_list"],
        tempImgs = ["wechat/home.jpg"]
    )
    fun homePage() {

    }
    
    @ScriptPage(
        name = "金币页面",
        ids = ["com.kuaishou.nebula:id/coin_container", "com.kuaishou.nebula:id/reward_center"],
    )
    fun coinPage() {

    }
} 