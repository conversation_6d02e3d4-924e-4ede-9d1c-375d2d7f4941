package com.jerome.supernuggetsmaster.scripts

import com.jerome.supernuggetsmaster.annotation.ScriptModel
import com.jerome.supernuggetsmaster.annotation.ScriptPage
import com.jerome.supernuggetsmaster.utils.AssistHelper
import com.jerome.supernuggetsmaster.utils.Logger
import android.view.accessibility.AccessibilityNodeInfo
import com.blankj.utilcode.util.ClipboardUtils
import kotlinx.coroutines.delay

@ScriptModel(
    id = "wechat",
    name = "暖暖孕迹",
    description = "获取免费使用时长",
    targetApp = "com.tencent.mm",
    icon = "💬",
    estimatedDuration = 240 // 秒为单位
)
class WeChatScript {

    @ScriptPage(
        name = "微信首页",
        tempImgs = ["wechat/page_main.png"],
    )
    suspend fun mainPage() {
        //com.tencent.mm:id/huj 是微信底部tab的id,它的4个子节点分别是 微信，通讯录，发现，我
        //要求通过下标进行这四个节点的定位

        //业务逻辑是 先点击 通讯录 等待3秒 再点击 我 等待3秒 再点击 发现 等待3秒，再点击微信 等待3秒，最后进行一个下拉操作

        try {
            // 等待底部导航栏加载
            delay(1000)

            // 检查底部tab容器是否存在
            val tabContainers = AssistHelper.findById("com.tencent.mm:id/nvt")
            if (tabContainers.isNullOrEmpty()) {
                Logger.e("未找到微信底部tab容器")
                return
            }

            // 1. 点击通讯录（下标1）
            Logger.i("步骤1: 点击通讯录")
            if (AssistHelper.clickNode(tabContainers[1])) {
                Logger.i("通讯录点击成功")
                delay(1000) // 等待3秒
            } else {
                Logger.e("通讯录点击失败")
            }

            // 2. 点击我（下标3）
            Logger.i("步骤2: 点击我")
            if (AssistHelper.clickNode(tabContainers[3])) {
                Logger.i("我点击成功")
                delay(1000) // 等待3秒
            } else {
                Logger.e("我点击失败")
            }

            // 3. 点击发现（下标2）
            Logger.i("步骤3: 点击发现")
            if (AssistHelper.clickNode(tabContainers[2])) {
                Logger.i("发现点击成功")
                delay(1000) // 等待3秒
            } else {
                Logger.e("发现点击失败")
            }

            // 4. 点击微信（下标0）
            Logger.i("步骤4: 点击微信")
            if (AssistHelper.clickNode(tabContainers[0])) {
                Logger.i("微信点击成功")
                delay(1000) // 等待3秒
            } else {
                Logger.e("微信点击失败")
            }

            // 5. 执行下拉操作
            Logger.i("步骤5: 执行下拉操作")
            if (AssistHelper.scrollDown()) {
                Logger.i("下拉操作成功")
            } else {
                Logger.e("下拉操作失败")
            }
        } catch (e: Exception) {
            Logger.e("执行微信首页操作时发生错误: ${e.message}")
            e.printStackTrace()
        }
    }

    @ScriptPage(
        name = "小程序最近历史页",
        tempImgs = ["wechat/page_minhistory.png"],
    )
    suspend fun minHistoryPage() {
        try {
            Logger.i("开始执行小程序最近历史页操作")

            // 等待页面加载
            delay(2000)

            // 方法1：通过内容描述查找搜索栏
            Logger.i("尝试通过内容描述查找搜索栏")
            val searchNode = AssistHelper.findByDesc("搜索小程序")
            if (searchNode != null) {
                Logger.i("找到搜索栏节点")
                if (AssistHelper.clickNode(searchNode)) {
                    Logger.i("搜索栏点击成功")
                    delay(2000)
                    return
                } else {
                    Logger.e("搜索栏点击失败")
                }
            }

            Logger.e("所有方法都无法找到搜索栏")

        } catch (e: Exception) {
            Logger.e("执行小程序最近历史页操作时发生错误: ${e.message}")
            e.printStackTrace()
        }
    }

    @ScriptPage(
        name = "小程序搜索页",
        tempImgs = ["wechat/page_min_real_search.png"],
    )
    suspend fun minSearchPage() {
        try {
            Logger.i("开始执行小程序搜索页操作")
            AssistHelper
            // 等待页面加载
            delay(2000)

            // 方法1：使用图像识别查找目标区域
            Logger.i("尝试使用图像识别查找目标区域")
            val success = AssistHelper.clickByImageRecognition("wechat/page_min_real_search.png")
            if (success) {
                Logger.i("图像识别点击成功")
                delay(2000)
                return
            } else {
                Logger.w("图像识别点击失败，尝试其他方法")
            }
        } catch (e: Exception) {
            Logger.e("执行小程序搜索页操作时发生错误: ${e.message}")
            e.printStackTrace()
        }
    }
}