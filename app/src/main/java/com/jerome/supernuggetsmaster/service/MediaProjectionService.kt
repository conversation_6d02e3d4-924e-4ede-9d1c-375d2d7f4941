package com.jerome.supernuggetsmaster.service

import android.app.Activity
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.PixelFormat
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.Image
import android.media.ImageReader
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.DisplayMetrics
import android.view.WindowManager
import androidx.core.app.NotificationCompat
import com.jerome.supernuggetsmaster.MainActivity
import com.jerome.supernuggetsmaster.R
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import kotlin.coroutines.resume

/**
 * 重新设计的媒体投影前台服务
 *
 * 核心策略：
 * 1. 在Service中维护一个长期有效的MediaProjection实例
 * 2. 提供截图接口，每次创建新的VirtualDisplay
 * 3. 避免MediaProjection实例的重复创建和销毁
 */
class MediaProjectionService : Service() {

    companion object {
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "media_projection_service"
        private const val CHANNEL_NAME = "屏幕录制服务"

        private const val ACTION_START = "ACTION_START"
        private const val ACTION_STOP = "ACTION_STOP"
        private const val ACTION_INIT_PROJECTION = "ACTION_INIT_PROJECTION"

        @Volatile
        private var instance: MediaProjectionService? = null

        /**
         * 启动媒体投影服务
         */
        fun start(context: Context) {
            val intent = Intent(context, MediaProjectionService::class.java).apply {
                action = ACTION_START
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        /**
         * 初始化MediaProjection
         */
        fun initializeMediaProjection(context: Context, resultCode: Int, data: Intent) {
            val intent = Intent(context, MediaProjectionService::class.java).apply {
                action = ACTION_INIT_PROJECTION
                putExtra("resultCode", resultCode)
                putExtra("data", data)
            }
            context.startService(intent)
        }

        /**
         * 停止媒体投影服务
         */
        fun stop(context: Context) {
            try {
                val intent = Intent(context, MediaProjectionService::class.java).apply {
                    action = ACTION_STOP
                }
                context.startService(intent)
            } catch (e: Exception) {
                // 忽略停止服务时的异常
            }
        }

        /**
         * 检查服务是否正在运行
         */
        fun isRunning(): Boolean = instance != null

        /**
         * 获取截图
         */
        suspend fun captureScreen(): Bitmap? {
            return instance?.performCapture()
        }
    }

    // MediaProjection相关
    private var mediaProjectionManager: MediaProjectionManager? = null
    private var mediaProjection: MediaProjection? = null

    // 截图互斥锁
    private val captureMutex = Mutex()

    // 主线程Handler
    private val handler = Handler(Looper.getMainLooper())
    
    override fun onCreate() {
        super.onCreate()
        instance = this
        mediaProjectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
        createNotificationChannel()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START -> {
                startForegroundService()
                return START_STICKY
            }
            ACTION_INIT_PROJECTION -> {
                val resultCode = intent.getIntExtra("resultCode", -1)
                val data = intent.getParcelableExtra<Intent>("data")
                if (resultCode == Activity.RESULT_OK && data != null) {
                    initializeMediaProjection(resultCode, data)
                }
                return START_STICKY
            }
            ACTION_STOP -> {
                stopForegroundService()
                return START_NOT_STICKY
            }
        }
        return START_NOT_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onDestroy() {
        super.onDestroy()
        cleanupMediaProjection()
        instance = null
    }

    /**
     * 初始化MediaProjection实例
     */
    private fun initializeMediaProjection(resultCode: Int, data: Intent) {
        try {
            // 清理之前的实例
            cleanupMediaProjection()

            // 创建新的MediaProjection实例
            mediaProjection = mediaProjectionManager?.getMediaProjection(resultCode, data)

            if (mediaProjection != null) {
                // 注册回调
                mediaProjection!!.registerCallback(object : MediaProjection.Callback() {
                    override fun onStop() {
                        super.onStop()
                        android.util.Log.d("MediaProjectionService", "MediaProjection已停止")
                        cleanupMediaProjection()
                    }
                }, handler)

                android.util.Log.i("MediaProjectionService", "MediaProjection初始化成功")
            } else {
                android.util.Log.e("MediaProjectionService", "MediaProjection初始化失败")
            }
        } catch (e: Exception) {
            android.util.Log.e("MediaProjectionService", "初始化MediaProjection时发生异常", e)
            cleanupMediaProjection()
        }
    }

    /**
     * 清理MediaProjection资源
     */
    private fun cleanupMediaProjection() {
        try {
            mediaProjection?.stop()
            mediaProjection = null
            android.util.Log.d("MediaProjectionService", "MediaProjection资源已清理")
        } catch (e: Exception) {
            android.util.Log.w("MediaProjectionService", "清理MediaProjection时发生异常", e)
        }
    }

    /**
     * 执行截图
     */
    suspend fun performCapture(): Bitmap? = captureMutex.withLock {
        val projection = mediaProjection
        if (projection == null) {
            android.util.Log.w("MediaProjectionService", "MediaProjection未初始化，无法截图")
            return@withLock null
        }

        return@withLock withContext(Dispatchers.Main) {
            var imageReader: ImageReader? = null
            var virtualDisplay: VirtualDisplay? = null

            try {
                android.util.Log.d("MediaProjectionService", "开始执行截图")

                // 获取屏幕信息
                val screenInfo = getScreenInfo()

                // 创建ImageReader
                imageReader = ImageReader.newInstance(
                    screenInfo.width,
                    screenInfo.height,
                    PixelFormat.RGBA_8888,
                    1
                )

                // 创建VirtualDisplay - 每次都创建新的
                val displayName = "ServiceCapture-${System.currentTimeMillis()}"
                virtualDisplay = projection.createVirtualDisplay(
                    displayName,
                    screenInfo.width,
                    screenInfo.height,
                    (screenInfo.density * 160).toInt(),
                    DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                    imageReader.surface,
                    null,
                    handler
                )

                android.util.Log.d("MediaProjectionService", "VirtualDisplay创建成功: $displayName")

                // 等待VirtualDisplay稳定
                kotlinx.coroutines.delay(500)

                // 等待图像数据
                val bitmap = waitForImage(imageReader)

                if (bitmap != null) {
                    android.util.Log.d("MediaProjectionService", "截图成功")
                } else {
                    android.util.Log.w("MediaProjectionService", "截图失败")
                }

                bitmap

            } catch (e: Exception) {
                android.util.Log.e("MediaProjectionService", "截图过程中发生异常", e)
                null
            } finally {
                // 释放资源
                try {
                    virtualDisplay?.release()
                    imageReader?.close()
                    android.util.Log.d("MediaProjectionService", "截图资源已释放")
                } catch (e: Exception) {
                    android.util.Log.w("MediaProjectionService", "释放截图资源时发生异常", e)
                }
            }
        }
    }

    /**
     * 获取屏幕信息
     */
    private fun getScreenInfo(): ScreenInfo {
        val windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val displayMetrics = DisplayMetrics()
        windowManager.defaultDisplay.getRealMetrics(displayMetrics)

        return ScreenInfo(
            width = displayMetrics.widthPixels,
            height = displayMetrics.heightPixels,
            density = displayMetrics.density
        )
    }

    /**
     * 屏幕信息数据类
     */
    data class ScreenInfo(
        val width: Int,
        val height: Int,
        val density: Float
    )

    /**
     * 等待ImageReader获取图像
     */
    private suspend fun waitForImage(imageReader: ImageReader): Bitmap? {
        return withTimeoutOrNull(10000) { // 10秒超时
            suspendCancellableCoroutine { continuation ->
                var isCompleted = false

                val listener = ImageReader.OnImageAvailableListener { reader ->
                    if (isCompleted) return@OnImageAvailableListener

                    try {
                        val image = reader.acquireLatestImage()
                        if (image != null) {
                            android.util.Log.d("MediaProjectionService", "收到图像数据，尺寸: ${image.width}x${image.height}")
                            val bitmap = imageToBitmap(image)
                            image.close()

                            if (continuation.isActive && !isCompleted) {
                                isCompleted = true
                                continuation.resume(bitmap)
                            }
                        } else {
                            android.util.Log.w("MediaProjectionService", "ImageReader返回null图像")
                            if (continuation.isActive && !isCompleted) {
                                isCompleted = true
                                continuation.resume(null)
                            }
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("MediaProjectionService", "处理图像时发生异常", e)
                        if (continuation.isActive && !isCompleted) {
                            isCompleted = true
                            continuation.resume(null)
                        }
                    }
                }

                // 设置监听器
                imageReader.setOnImageAvailableListener(listener, handler)
                android.util.Log.d("MediaProjectionService", "ImageReader监听器已设置，等待图像数据...")

                continuation.invokeOnCancellation {
                    android.util.Log.d("MediaProjectionService", "截图等待被取消")
                    imageReader.setOnImageAvailableListener(null, null)
                    isCompleted = true
                }
            }
        }
    }

    /**
     * 将Image转换为Bitmap
     */
    private fun imageToBitmap(image: Image): Bitmap? {
        return try {
            val planes = image.planes
            val buffer = planes[0].buffer
            val pixelStride = planes[0].pixelStride
            val rowStride = planes[0].rowStride
            val rowPadding = rowStride - pixelStride * image.width

            val bitmap = Bitmap.createBitmap(
                image.width + rowPadding / pixelStride,
                image.height,
                Bitmap.Config.ARGB_8888
            )
            bitmap.copyPixelsFromBuffer(buffer)

            // 如果有padding，需要裁剪
            if (rowPadding != 0) {
                Bitmap.createBitmap(bitmap, 0, 0, image.width, image.height)
            } else {
                bitmap
            }
        } catch (e: Exception) {
            android.util.Log.e("MediaProjectionService", "转换图像为Bitmap时发生异常", e)
            null
        }
    }
    
    /**
     * 启动前台服务
     */
    private fun startForegroundService() {
        val notification = createNotification()
        startForeground(NOTIFICATION_ID, notification)
    }
    
    /**
     * 停止前台服务
     */
    private fun stopForegroundService() {
        stopForeground(true)
        stopSelf()
    }
    
    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "屏幕录制服务通知"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    /**
     * 创建前台服务通知
     */
    private fun createNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("屏幕录制服务运行中")
            .setContentText("正在为图像识别功能提供屏幕录制支持")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .build()
    }
}
